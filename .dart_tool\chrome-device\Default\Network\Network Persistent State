{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 20688}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 24577}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 21051}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 22597}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 28083}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399520929512595", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 17881}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://pro.fontawesome.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://www.clarity.ms", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://d3h1lg3ksw6i6b.cloudfront.net", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://cdn.branch.io", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://app.link", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://guide.michelin.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://api2.branch.io", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://ak.sail-horizon.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "**************118", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://widget.intercom.io", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://dynamic.criteo.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://c.bing.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://c.clarity.ms", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022883364900", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://js.intercomcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://api.sail-personalize.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", true, 0], "server": "https://gum.criteo.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://load.sumome.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://sumome.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://t.teads.tv", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://sslwidget.criteo.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", true, 0], "server": "https://client-side-metrics.da.us.criteo.net", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "server": "https://measurement-api.criteo.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022896628501", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2luc3RhZ3JhbS5jb20AAAA=", false, 0], "server": "https://graph.instagram.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022882343388", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 22001}, "server": "https://sdk.privacy-center.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528482818010", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 23804}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528482807241", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", true, 0], "network_stats": {"srtt": 41394}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528483024523", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 33253}, "server": "https://pagead2.googlesyndication.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022883175020", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 51667}, "server": "https://api.privacy-center.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022883197722", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 30555}, "server": "https://connect.facebook.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528483290493", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", true, 0], "network_stats": {"srtt": 26922}, "server": "https://michelin-guide-web.nw.r.appspot.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022883476626", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 32251}, "server": "https://www.facebook.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528483587809", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 22303}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528484671184", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 57137}, "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399528484769437", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL21pY2hlbGluLmNvbQ==", false, 0], "network_stats": {"srtt": 54497}, "server": "https://michelin-guide-web.nw.r.appspot.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022889199824", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2luc3RhZ3JhbS5jb20AAAA=", false, 0], "network_stats": {"srtt": 28191}, "server": "https://scontent-sea5-1.cdninstagram.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022889266916", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2luc3RhZ3JhbS5jb20AAAA=", false, 0], "network_stats": {"srtt": 21339}, "server": "https://scontent-sea1-1.cdninstagram.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022889312285", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2luc3RhZ3JhbS5jb20AAAA=", false, 0], "network_stats": {"srtt": 28891}, "server": "https://static.cdninstagram.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397022925185918", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2luc3RhZ3JhbS5jb20AAAA=", false, 0], "network_stats": {"srtt": 22766}, "server": "https://www.instagram.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 20324}, "server": "https://www.google.com"}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G", "CAYSABiAgICA+P////8B": "Offline"}}}