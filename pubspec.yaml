name: qsr_app
description: A new Flutter project for QSR ordering.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  http: ^1.4.0 # For making API calls to backend
  provider: ^6.0.0 # For managing the app's state
  flutter_animate: ^4.5.0 # For GSAP-like animations
  flutter_staggered_animations: ^1.1.1 # For staggered list animations
  visibility_detector: ^0.4.0+2 # For scroll-triggered animations
  animations: ^2.0.11 # For page transitions

  # Firebase
  firebase_core: ^2.27.0 # Firebase core
  firebase_messaging: ^14.8.1 # Firebase Cloud Messaging

  # Local Notifications (for testing and offline notifications)
  flutter_local_notifications: ^17.2.2 # Local notifications
  timezone: ^0.9.2 # For scheduling notifications at specific times

  # Deep Linking (to open specific pages from notifications)
  

  # Authentication & Security (Web-compatible versions)
  # local_auth: ^2.1.7 # Biometric authentication (mobile only)
  # google_sign_in: ^6.1.6 # Google Sign-In (temporarily disabled)
  # sign_in_with_apple: ^5.0.0 # Apple Sign-In (iOS only)

  # Payment Processing (Stripe temporarily disabled for testing)
  # flutter_stripe: ^9.5.0 # Stripe payments

  # Additional utilities
  shared_preferences: ^2.2.2 # Local storage
  connectivity_plus: ^5.0.2 # Network connectivity
  dio: ^5.4.0 # Advanced HTTP client
  web_socket_channel: ^2.4.0 # WebSocket support for real-time notifications

  # Accessibility & WCAG 2.1 AA Compliance
  flutter_accessibility_service: ^0.2.0 # Accessibility service integration
  accessibility_tools: ^2.2.1 # Accessibility testing and validation tools
  flutter_tts: ^3.8.3 # Text-to-speech for screen reader support
  speech_to_text: ^6.6.0 # Voice input accessibility
  vibration: ^1.8.4 # Haptic feedback for accessibility

  # Game Integration (temporarily disabled for dependency resolution)
  # flutter_inappwebview: ^5.8.0 # Advanced WebView with better game support
  permission_handler: ^11.1.0 # For camera permissions and Social Rewards
  # wakelock_plus: ^1.2.5 # Keep screen awake during games

  # Social Rewards Feature Dependencies
  image_picker: ^1.0.4 # Camera access for taking photos/videos
  share_plus: ^7.2.1 # Social media sharing functionality
  flutter_image_compress: ^2.1.0 # Simple photo filters and compression

  # Revel Systems API Integration
  json_annotation: ^4.8.1 # JSON serialization
  crypto: ^3.0.3 # For API signature generation
  uuid: ^4.2.1 # For unique request IDs
  flutter_secure_storage: ^9.0.0 # Secure token storage
  url_launcher: ^6.2.2 # For payment redirects

  # Offline storage
  hive: ^2.2.3 # NoSQL local database
  hive_flutter: ^1.1.0 # Hive Flutter integration
  sqflite: ^2.3.0 # SQLite local database
  path: ^1.8.3 # Path utilities
  cached_network_image: ^3.3.0 # For image caching
  flutter_cache_manager: ^3.3.1 # Cache management
  image: ^4.1.3 # Image processing

  # Crash Reporting & Analytics (temporarily disabled for dependency resolution)
  # sentry_flutter: ^7.14.0
  # device_info_plus: ^9.1.1
  # package_info_plus: ^4.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  flutter_lints: ^2.0.0

  # Testing & QA
  mockito: ^5.4.5
  build_runner: ^2.4.9
  fake_async: ^1.3.1
  flutter_driver:
    sdk: flutter
  http_mock_adapter: ^0.6.1
  golden_toolkit: ^0.15.0

  # Accessibility Testing & Compliance
  flutter_accessibility_test: ^0.1.0 # Automated accessibility testing
  axe_core_flutter: ^1.0.0 # axe-core accessibility engine for Flutter
  wcag_color_contrast: ^1.0.0 # WCAG color contrast validation

  # Code generation
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1 # JSON code generation for Revel API models

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/CC-Penta-3.png
    - assets/games/
    - assets/games/chicken_catch/

  fonts:
    - family: SofiaRoughBlackThree
      fonts:
        - asset: assets/fonts/SofiaRoughBlackThree.ttf
          weight: 900
    
    - family: MontserratBlack
      fonts:
        - asset: assets/fonts/Montserrat-Black.ttf
          weight: 900
        - asset: assets/fonts/Montserrat-BlackItalic.ttf
          weight: 900
          style: italic
    
    - family: SofiaSans
      fonts:
        - asset: assets/fonts/SofiaSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/SofiaSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/SofiaSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/SofiaSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/SofiaSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/SofiaSans-Black.ttf
          weight: 900
