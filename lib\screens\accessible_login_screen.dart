import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/mock_auth_service.dart';
import '../services/accessibility_service.dart';
import '../widgets/accessibility_widgets.dart';
import '../models/auth_state.dart';

/// 🔐 WCAG 2.1 AA Compliant Login Screen for CHICA'S Chicken
/// Implements comprehensive accessibility features including:
/// - Screen reader support with proper semantic labels
/// - Keyboard navigation with focus management
/// - High contrast mode compatibility
/// - Text scaling support
/// - Error announcements with live regions
/// - Haptic feedback for interactions
class AccessibleLoginScreen extends StatefulWidget {
  const AccessibleLoginScreen({Key? key}) : super(key: key);

  @override
  State<AccessibleLoginScreen> createState() => _AccessibleLoginScreenState();
}

class _AccessibleLoginScreenState extends State<AccessibleLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _loginButtonFocusNode = FocusNode();
  
  bool _isLoading = false;
  bool _rememberMe = false;
  bool _obscurePassword = true;
  String? _emailError;
  String? _passwordError;
  String? _generalError;
  
  late AnimationController _fadeController;
  late AnimationController _progressController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupFocusListeners();
    
    // Announce screen to screen readers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _announceScreenLoad();
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
    
    _fadeController.forward();
  }

  void _setupFocusListeners() {
    _emailFocusNode.addListener(() {
      if (_emailFocusNode.hasFocus) {
        _clearEmailError();
      }
    });
    
    _passwordFocusNode.addListener(() {
      if (_passwordFocusNode.hasFocus) {
        _clearPasswordError();
      }
    });
  }

  void _announceScreenLoad() {
    final accessibilityService = Provider.of<AccessibilityService>(context, listen: false);
    accessibilityService.announce(
      'Login screen loaded. Enter your email and password to sign in to CHICA\'S Chicken.',
    );
  }

  void _clearEmailError() {
    if (_emailError != null) {
      setState(() {
        _emailError = null;
      });
    }
  }

  void _clearPasswordError() {
    if (_passwordError != null) {
      setState(() {
        _passwordError = null;
      });
    }
  }

  Future<void> _handleLogin() async {
    final accessibilityService = Provider.of<AccessibilityService>(context, listen: false);
    
    // Clear previous errors
    setState(() {
      _emailError = null;
      _passwordError = null;
      _generalError = null;
    });

    if (!_formKey.currentState!.validate()) {
      accessibilityService.announce('Please correct the errors in the form');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    _progressController.forward();
    accessibilityService.announce('Signing in, please wait');

    try {
      final authService = Provider.of<MockAuthService>(context, listen: false);
      final result = await authService.signInWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (result.hasError) {
        setState(() {
          _generalError = result.errorMessage;
        });
        accessibilityService.announce('Login failed: ${result.errorMessage}');
        accessibilityService.hapticFeedback();
      } else if (result.isAuthenticated) {
        accessibilityService.announce('Login successful! Welcome back to CHICA\'S Chicken');
        // Navigation will be handled by AuthWrapper
      }
    } catch (e) {
      setState(() {
        _generalError = 'Login failed. Please try again.';
      });
      accessibilityService.announce('Login failed. Please try again.');
      accessibilityService.hapticFeedback();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _progressController.reset();
      }
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AccessibilityService>(
      builder: (context, accessibilityService, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            title: const Text('Sign In'),
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
              tooltip: 'Go back',
            ),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: accessibilityService.getRecommendedPadding(),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Progress indicator for loading state
                      if (_isLoading) ...[
                        Semantics(
                          label: 'Signing in progress',
                          child: AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (context, child) {
                              return LinearProgressIndicator(
                                value: _progressAnimation.value,
                                backgroundColor: Colors.grey[300],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).primaryColor,
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Header
                      _buildHeader(accessibilityService),
                      
                      const SizedBox(height: 32),
                      
                      // General error message
                      if (_generalError != null) ...[
                        Semantics(
                          liveRegion: true,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.error,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Theme.of(context).colorScheme.error,
                                  semanticLabel: 'Error',
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _generalError!,
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.error,
                                      fontSize: 14 * accessibilityService.textScaleFactor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // Email Field
                      AccessibleFormField(
                        label: 'Email Address',
                        hint: 'Enter your email address',
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        required: true,
                        validator: _validateEmail,
                        errorText: _emailError,
                        enabled: !_isLoading,
                        semanticLabel: 'Email address, required field',
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Password Field
                      AccessibleFormField(
                        label: 'Password',
                        hint: 'Enter your password',
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        required: true,
                        validator: _validatePassword,
                        errorText: _passwordError,
                        enabled: !_isLoading,
                        semanticLabel: 'Password, required field',
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                            accessibilityService.announce(
                              _obscurePassword ? 'Password hidden' : 'Password visible',
                            );
                          },
                          tooltip: _obscurePassword ? 'Show password' : 'Hide password',
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Remember Me and Forgot Password
                      _buildOptionsRow(accessibilityService),
                      
                      const SizedBox(height: 32),
                      
                      // Login Button
                      WCAGButton(
                        onPressed: _isLoading ? null : _handleLogin,
                        isLoading: _isLoading,
                        semanticLabel: _isLoading ? 'Signing in, please wait' : 'Sign in to your account',
                        child: Text(
                          _isLoading ? 'Signing In...' : 'Sign In',
                          style: TextStyle(
                            fontSize: 16 * accessibilityService.textScaleFactor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Sign up link
                      _buildSignUpLink(accessibilityService),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(AccessibilityService accessibilityService) {
    return Semantics(
      header: true,
      child: Column(
        children: [
          // Logo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.restaurant,
              size: 40,
              color: Colors.white,
              semanticLabel: 'CHICA\'S Chicken logo',
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Title
          Text(
            'Welcome Back!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 28 * accessibilityService.textScaleFactor,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Subtitle
          Text(
            'Sign in to your CHICA\'S Chicken account',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
              fontSize: 16 * accessibilityService.textScaleFactor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsRow(AccessibilityService accessibilityService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Remember Me Checkbox
        Semantics(
          label: 'Remember me checkbox, ${_rememberMe ? 'checked' : 'unchecked'}',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: accessibilityService.getMinimumTouchTargetSize(),
                height: accessibilityService.getMinimumTouchTargetSize(),
                child: Checkbox(
                  value: _rememberMe,
                  onChanged: _isLoading ? null : (value) {
                    setState(() {
                      _rememberMe = value ?? false;
                    });
                    accessibilityService.announce(
                      _rememberMe ? 'Remember me enabled' : 'Remember me disabled',
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Remember me',
                style: TextStyle(
                  fontSize: 14 * accessibilityService.textScaleFactor,
                ),
              ),
            ],
          ),
        ),
        
        // Forgot Password Link
        WCAGButton(
          type: ButtonType.text,
          onPressed: _isLoading ? null : () {
            Navigator.pushNamed(context, '/password-reset');
          },
          semanticLabel: 'Forgot password? Reset your password',
          child: Text(
            'Forgot Password?',
            style: TextStyle(
              fontSize: 14 * accessibilityService.textScaleFactor,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpLink(AccessibilityService accessibilityService) {
    return Semantics(
      label: 'Don\'t have an account? Sign up for CHICA\'S Chicken',
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Don\'t have an account? ',
            style: TextStyle(
              fontSize: 14 * accessibilityService.textScaleFactor,
              color: Colors.grey[600],
            ),
          ),
          WCAGButton(
            type: ButtonType.text,
            onPressed: () {
              Navigator.pushNamed(context, '/signup');
            },
            semanticLabel: 'Sign up for a new account',
            child: Text(
              'Sign Up',
              style: TextStyle(
                fontSize: 14 * accessibilityService.textScaleFactor,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _progressController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _loginButtonFocusNode.dispose();
    super.dispose();
  }
}
